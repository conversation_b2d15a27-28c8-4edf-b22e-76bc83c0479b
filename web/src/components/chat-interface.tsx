"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, Send, Bot, User, Loader2, StopCircle } from "lucide-react";
import { Client } from "@langchain/langgraph-sdk";
import { v4 as uuidv4 } from "uuid";
import { toast } from "sonner";
import { inputDesignItems } from "@/components/constants";

interface ChatInterfaceProps {
  onBack: () => void;
}

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

export function ChatInterface({ onBack }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [threadId, setThreadId] = useState<string>(() => uuidv4());
  const [isLoading, setIsLoading] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const abortController = useRef<AbortController | null>(null);

  // 初始化 LangGraph Client
  const client = new Client({
    apiUrl: "http://localhost:3000/api", // 使用本地代理
  });

  // 滚动到底部
  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: uuidv4(),
      role: "user",
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = input.trim();
    setInput("");
    setIsLoading(true);

    // 添加加载状态的助手消息
    const assistantMessageId = uuidv4();
    const loadingMessage: Message = {
      id: assistantMessageId,
      role: "assistant",
      content: "正在思考中...",
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, loadingMessage]);

    try {
      // 创建 AbortController 用于取消请求
      abortController.current = new AbortController();

      // 调用 LangGraph API
      const stream = client.runs.stream(
        threadId,
        "designToCode",
        {
          // 注意：使用mock的数据
          input: inputDesignItems, // { messages: [{ role: "human", content: currentInput }] },
          streamMode: "messages",
          signal: abortController.current.signal,
        }
      );

      let assistantResponse = "";
      let hasContent = false;

      for await (const chunk of stream) {
        if (chunk.event === "messages/partial") {
          const data = chunk.data as any;
          if (data && data.content && typeof data.content === "string") {
            assistantResponse = data.content;
            hasContent = true;
            
            // 更新最后一条助手消息
            setMessages(prev => {
              const newMessages = [...prev];
              const lastMessage = newMessages[newMessages.length - 1];
              if (lastMessage && lastMessage.id === assistantMessageId) {
                lastMessage.content = assistantResponse;
              }
              return newMessages;
            });
          }
        } else if (chunk.event === "messages/complete") {
          const data = chunk.data as any;
          if (data && data.content && typeof data.content === "string") {
            assistantResponse = data.content;
            hasContent = true;
          }
        }
      }

      // 如果没有接收到内容，显示默认消息
      if (!hasContent) {
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMessage = newMessages[newMessages.length - 1];
          if (lastMessage && lastMessage.id === assistantMessageId) {
            lastMessage.content = "Agent已完成处理，但未返回内容。";
          }
          return newMessages;
        });
      }

    } catch (error: any) {
      console.error("Error sending message:", error);
      
      if (error?.name === "AbortError") {
        // 请求被取消
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMessage = newMessages[newMessages.length - 1];
          if (lastMessage && lastMessage.id === assistantMessageId) {
            lastMessage.content = "请求已取消。";
          }
          return newMessages;
        });
      } else {
        // 其他错误
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMessage = newMessages[newMessages.length - 1];
          if (lastMessage && lastMessage.id === assistantMessageId) {
            lastMessage.content = "抱歉，发生了错误。请稍后重试。";
          }
          return newMessages;
        });
        
        toast.error("发送消息失败", {
          description: error?.message || "请检查网络连接和服务状态",
        });
      }
    } finally {
      setIsLoading(false);
      abortController.current = null;
    }
  };

  const handleStopStream = () => {
    if (abortController.current) {
      abortController.current.abort();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-4 h-screen flex flex-col">
        {/* Header */}
        <div className="flex items-center gap-4 mb-4">
          <Button variant="outline" size="icon" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">LangGraph Agent 对话</h1>
            <p className="text-muted-foreground text-sm">
              Thread ID: {threadId}
            </p>
          </div>
        </div>

        {/* Chat Area */}
        <Card className="flex-1 flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              designToCode Agent
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col p-0">
            <ScrollArea className="flex-1 px-6" ref={scrollAreaRef}>
              <div className="space-y-4 py-4">
                {messages.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>开始与 LangGraph Agent 对话</p>
                    <p className="text-sm mt-2">
                      我可以帮助您进行设计到代码的转换
                    </p>
                  </div>
                )}

                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.role === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`flex gap-3 max-w-[80%] ${
                        message.role === "user" ? "flex-row-reverse" : "flex-row"
                      }`}
                    >
                      <div className="flex-shrink-0">
                        {message.role === "user" ? (
                          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                            <User className="h-4 w-4 text-primary-foreground" />
                          </div>
                        ) : (
                          <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                            <Bot className="h-4 w-4 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      <div
                        className={`rounded-lg px-4 py-2 ${
                          message.role === "user"
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted"
                        }`}
                      >
                        <div className="whitespace-pre-wrap break-words">
                          {message.content}
                        </div>
                        <div
                          className={`text-xs mt-1 opacity-70 ${
                            message.role === "user"
                              ? "text-primary-foreground/70"
                              : "text-muted-foreground"
                          }`}
                        >
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

            {/* Input Area */}
            <div className="border-t p-4">
              <div className="flex gap-2">
                <Input
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="输入您的消息...(随便输入，已mock输入)"
                  className="flex-1"
                  disabled={isLoading}
                />
                {isLoading ? (
                  <Button onClick={handleStopStream} variant="destructive">
                    <StopCircle className="h-4 w-4" />
                  </Button>
                ) : (
                  <Button onClick={handleSendMessage} disabled={!input.trim()}>
                    <Send className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div className="text-xs text-muted-foreground mt-2 text-center">
                {isLoading 
                  ? "Agent正在处理中... 点击停止按钮可取消"
                  : "按 Enter 发送消息 • Shift + Enter 换行"
                }
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
