"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useStream } from "@langchain/langgraph-sdk/react";
import { v4 as uuidv4 } from "uuid";

// 常量定义，与open-swe保持一致
const DO_NOT_RENDER_ID_PREFIX = "__";

export default function TestChatPage() {
  const [input, setInput] = useState("");
  const [isClient, setIsClient] = useState(false);

  // 生成一个标准的UUID作为threadId
  const [threadId] = useState(() => uuidv4());

  // 确保只在客户端渲染
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 使用 useStream hook 连接到 simpleChat assistant，与open-swe保持一致
  const stream = useStream<any>({
    apiUrl: process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:3000/api",
    assistantId: "simpleChat",
    threadId: threadId,
    reconnectOnMount: true,
    fetchStateHistory: false,
  });

  // 从 stream 中获取消息和状态，与open-swe保持一致
  const messages = (stream.messages || []) as any[];
  const isLoading = stream.isLoading;
  const error = stream.error;

  const handleSendMessage = () => {
    if (!input.trim() || isLoading) return;

    const userInput = input.trim();
    console.log("发送消息:", userInput);

    // 使用simpleChat图期望的输入格式，确保input是字符串
    stream.submit(
      {
        input: userInput,
        messages: [],
        output: "",
      },
      {
        streamResumable: true,
      }
    );

    setInput("");
  };

  // 过滤掉不需要渲染的消息，参考open-swe的DO_NOT_RENDER_ID_PREFIX
  const filteredMessages = messages.filter((message) => {
    return !message.id?.startsWith(DO_NOT_RENDER_ID_PREFIX);
  });

  // 如果不在客户端，显示加载状态
  if (!isClient) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">SimpleChat 测试页面</h1>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">正在加载...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">SimpleChat 测试页面</h1>
      
      <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
        {filteredMessages.map((message) => (
          <div
            key={message.id || `msg-${Math.random()}`}
            className={`p-4 rounded-lg ${
              message.type === "human"
                ? "bg-blue-100 ml-8"
                : "bg-gray-100 mr-8"
            }`}
          >
            <div className="font-medium mb-1">
              {message.type === "human" ? "您" : "AI 助手"}
            </div>
            <div className="whitespace-pre-wrap">
              {Array.isArray(message.content)
                ? message.content.map((content: any, index: number) => {
                    if (typeof content === "string") {
                      return <span key={index}>{content}</span>;
                    } else if (content.type === "text") {
                      return <span key={index}>{content.text}</span>;
                    }
                    return null;
                  }).join("")
                : typeof message.content === "string"
                ? message.content
                : JSON.stringify(message.content)
              }
            </div>
            <div className="text-xs text-gray-500 mt-2">
              {message.timestamp ? new Date(message.timestamp).toLocaleTimeString() : new Date().toLocaleTimeString()}
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="bg-gray-100 p-4 rounded-lg mr-8">
            <div className="font-medium mb-1">AI 助手</div>
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" />
              <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
              <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
            </div>
          </div>
        )}
      </div>

      <div className="flex gap-2">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
          placeholder="输入您的消息..."
          className="flex-1"
          disabled={isLoading}
        />
        <Button onClick={handleSendMessage} disabled={!input.trim() || isLoading}>
          发送
        </Button>
      </div>

      <div className="mt-4 text-sm text-gray-500">
        按 Enter 发送消息 • 当前状态: {isLoading ? "处理中..." : "就绪"}
      </div>

      {/* 错误显示 */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="font-medium text-red-800 mb-2">错误信息</h3>
          <div className="text-sm text-red-600">
            {typeof error === 'string' ? error : JSON.stringify(error)}
          </div>
        </div>
      )}

      {/* 调试信息 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium mb-2">调试信息</h3>
        <div className="text-xs space-y-1">
          <div>线程ID: {threadId}</div>
          <div>运行状态: {isLoading ? "运行中" : "空闲"}</div>
          <div>消息数量: {filteredMessages.length}</div>
          <div>原始消息数量: {messages.length}</div>
          <div>错误: {error ? JSON.stringify(error) : "无"}</div>
        </div>
      </div>
    </div>
  );
}
