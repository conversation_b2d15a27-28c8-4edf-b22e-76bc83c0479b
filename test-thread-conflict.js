/**
 * 测试脚本：验证 Thread 冲突错误修复
 * 
 * 这个脚本会模拟快速连续发送多个请求到同一个 thread，
 * 验证我们的修复是否能正确处理 "Thread is already running a task" 错误
 */

import { Client } from "@langchain/langgraph-sdk";
import { v4 as uuidv4 } from "uuid";

async function testThreadConflict() {
  console.log("🧪 开始测试 Thread 冲突处理...\n");

  const client = new Client({
    apiUrl: "http://localhost:2024",
  });

  const threadId = uuidv4(); // 使用 UUID 格式
  const assistantId = "designToCode";
  
  console.log(`📝 使用 Thread ID: ${threadId}`);
  console.log(`🤖 使用 Assistant ID: ${assistantId}\n`);

  // 测试输入
  const testInput = {
    input: [
      {
        pageName: "测试页面",
        pageContent: "<div>测试内容</div>",
        type: "html"
      }
    ]
  };

  try {
    console.log("🚀 发送第一个请求...");
    const run1Promise = client.runs.create(threadId, assistantId, { 
      input: testInput 
    });

    // 立即发送第二个请求（应该触发冲突）
    console.log("⚡ 立即发送第二个请求（预期会冲突）...");
    const run2Promise = client.runs.create(threadId, assistantId, { 
      input: testInput 
    });

    // 等待两个请求的结果
    const results = await Promise.allSettled([run1Promise, run2Promise]);

    console.log("\n📊 测试结果:");
    console.log("================");

    results.forEach((result, index) => {
      console.log(`\n请求 ${index + 1}:`);
      if (result.status === 'fulfilled') {
        console.log(`✅ 成功: Run ID = ${result.value.run_id}`);
      } else {
        console.log(`❌ 失败: ${result.reason.message}`);
        
        // 检查是否是预期的 thread 冲突错误
        if (result.reason.message.includes("Thread is already running a task")) {
          console.log("🎯 这是预期的 Thread 冲突错误 - 测试通过！");
        } else {
          console.log("⚠️  这是意外的错误类型");
        }
      }
    });

    // 等待第一个请求完成
    if (results[0].status === 'fulfilled') {
      console.log("\n⏳ 等待第一个请求完成...");
      const run1 = results[0].value;
      await client.runs.join(threadId, run1.run_id);
      console.log("✅ 第一个请求已完成");

      // 现在尝试发送新请求（应该成功）
      console.log("\n🔄 发送新请求（现在应该成功）...");
      const run3 = await client.runs.create(threadId, assistantId, { 
        input: testInput 
      });
      console.log(`✅ 新请求成功: Run ID = ${run3.run_id}`);
    }

  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message);
  }

  console.log("\n🏁 测试完成！");
}

// 运行测试
testThreadConflict().catch(console.error);
