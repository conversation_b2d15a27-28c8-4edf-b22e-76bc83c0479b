/**
 * 测试前端API代理的 Thread 冲突处理
 */

import { v4 as uuidv4 } from "uuid";

async function testFrontendAPI() {
  console.log("🧪 开始测试前端API代理的 Thread 冲突处理...\n");

  const threadId = uuidv4();
  const baseUrl = "http://localhost:3000/api";
  
  console.log(`📝 使用 Thread ID: ${threadId}`);
  console.log(`🌐 API Base URL: ${baseUrl}\n`);

  // 测试输入
  const testInput = {
    input: [
      {
        pageName: "测试页面",
        pageContent: "<div>测试内容</div>",
        type: "html"
      }
    ]
  };

  try {
    console.log("🚀 通过前端API发送第一个请求...");
    const request1 = fetch(`${baseUrl}/threads/${threadId}/runs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        assistant_id: 'designToCode',
        input: testInput,
        stream_mode: 'messages'
      })
    });

    // 立即发送第二个请求
    console.log("⚡ 立即通过前端API发送第二个请求（预期会冲突）...");
    const request2 = fetch(`${baseUrl}/threads/${threadId}/runs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        assistant_id: 'designToCode',
        input: testInput,
        stream_mode: 'messages'
      })
    });

    // 等待两个请求的结果
    const results = await Promise.allSettled([request1, request2]);

    console.log("\n📊 前端API测试结果:");
    console.log("========================");

    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      console.log(`\n请求 ${i + 1}:`);
      
      if (result.status === 'fulfilled') {
        const response = result.value;
        console.log(`📡 HTTP状态: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
          console.log("✅ 请求成功");
        } else {
          const errorText = await response.text();
          console.log(`❌ 请求失败: ${errorText}`);
          
          // 检查是否是预期的 thread 冲突错误
          if (errorText.includes("Thread is already running a task") || response.status === 422) {
            console.log("🎯 这是预期的 Thread 冲突错误 - 前端API正确处理！");
          } else {
            console.log("⚠️  这是意外的错误类型");
          }
        }
      } else {
        console.log(`❌ 网络错误: ${result.reason.message}`);
      }
    }

  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message);
  }

  console.log("\n🏁 前端API测试完成！");
}

// 运行测试
testFrontendAPI().catch(console.error);
