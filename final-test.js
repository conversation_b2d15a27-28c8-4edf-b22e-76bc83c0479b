/**
 * 最终验证测试
 */

import { v4 as uuidv4 } from "uuid";

async function testThreadConflictHandling() {
  console.log("🎯 最终验证: Thread 冲突修复效果");
  console.log("=" .repeat(50));
  
  const threadId = uuidv4();
  const baseUrl = "http://localhost:3000/api";
  
  console.log(`📝 Thread ID: ${threadId}`);
  
  try {
    console.log("🚀 发送第一个请求...");
    const request1 = fetch(`${baseUrl}/threads/${threadId}/runs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        assistant_id: 'designToCode',
        input: {
          input: [{
            pageName: "测试页面",
            pageContent: "<div>测试内容</div>",
            type: "html"
          }]
        },
        stream_mode: 'messages'
      })
    });

    console.log("⚡ 立即发送第二个请求...");
    const request2 = fetch(`${baseUrl}/threads/${threadId}/runs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        assistant_id: 'designToCode',
        input: {
          input: [{
            pageName: "测试页面2",
            pageContent: "<div>测试内容2</div>",
            type: "html"
          }]
        },
        stream_mode: 'messages'
      })
    });

    const [response1, response2] = await Promise.all([request1, request2]);
    
    console.log("\n📊 结果分析:");
    console.log(`请求1: HTTP ${response1.status} ${response1.statusText}`);
    console.log(`请求2: HTTP ${response2.status} ${response2.statusText}`);
    
    let conflictDetected = false;
    
    if (!response1.ok) {
      const error1 = await response1.text();
      if (error1.includes("Thread is already running a task")) {
        console.log("✅ 请求1: 正确检测到 Thread 冲突");
        conflictDetected = true;
      }
    }
    
    if (!response2.ok) {
      const error2 = await response2.text();
      if (error2.includes("Thread is already running a task")) {
        console.log("✅ 请求2: 正确检测到 Thread 冲突");
        conflictDetected = true;
      }
    }
    
    if (conflictDetected) {
      console.log("\n🎉 修复验证成功！");
      console.log("✅ 系统正确处理了 Thread 冲突错误");
      console.log("✅ 前端应该能够显示友好的错误消息");
      console.log("✅ 自动重试机制应该能够正常工作");
    } else {
      console.log("\n⚠️  未检测到预期的 Thread 冲突");
    }
    
  } catch (error) {
    console.error("❌ 测试失败:", error.message);
  }
  
  console.log("\n🏁 测试完成！");
}

testThreadConflictHandling().catch(console.error);
